import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface RaffleItem {
  id: number;
  title: string;
  description: string;
  image: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  endDate: Date;
  category: 'sneakers' | 'electronics' | 'fashion' | 'gaming';
  featured: boolean;
}

@Component({
  selector: 'app-raffle-platform',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './raffle-platform.component.html',
  styleUrls: ['./raffle-platform.component.css']
})
export class RafflePlatformComponent {
  
  raffleItems: RaffleItem[] = [
    {
      id: 1,
      title: 'Nike Air Jordan 1 Retro High OG',
      description: 'Chicago colorway - Size 9-12 available',
      image: '👟',
      price: 25,
      totalTickets: 1000,
      soldTickets: 847,
      endDate: new Date('2024-07-20T23:59:59'),
      category: 'sneakers',
      featured: true
    },
    {
      id: 2,
      title: 'iPhone 15 Pro Max',
      description: '256GB - Natural Titanium',
      image: '📱',
      price: 50,
      totalTickets: 500,
      soldTickets: 423,
      endDate: new Date('2024-07-18T23:59:59'),
      category: 'electronics',
      featured: true
    },
    {
      id: 3,
      title: 'PlayStation 5 Console',
      description: 'Latest model with 2 controllers',
      image: '🎮',
      price: 30,
      totalTickets: 800,
      soldTickets: 654,
      endDate: new Date('2024-07-22T23:59:59'),
      category: 'gaming',
      featured: false
    },
    {
      id: 4,
      title: 'Adidas Yeezy Boost 350 V2',
      description: 'Zebra colorway - Limited edition',
      image: '👟',
      price: 35,
      totalTickets: 600,
      soldTickets: 489,
      endDate: new Date('2024-07-25T23:59:59'),
      category: 'sneakers',
      featured: false
    },
    {
      id: 5,
      title: 'MacBook Pro 14"',
      description: 'M3 Pro chip, 18GB RAM, 512GB SSD',
      image: '💻',
      price: 75,
      totalTickets: 300,
      soldTickets: 267,
      endDate: new Date('2024-07-28T23:59:59'),
      category: 'electronics',
      featured: true
    },
    {
      id: 6,
      title: 'Supreme Box Logo Hoodie',
      description: 'Red colorway - Size M-XL available',
      image: '👕',
      price: 40,
      totalTickets: 400,
      soldTickets: 312,
      endDate: new Date('2024-07-24T23:59:59'),
      category: 'fashion',
      featured: false
    }
  ];

  selectedCategory: string = 'all';
  
  get filteredRaffles() {
    if (this.selectedCategory === 'all') {
      return this.raffleItems;
    }
    return this.raffleItems.filter(item => item.category === this.selectedCategory);
  }

  get featuredRaffles() {
    return this.raffleItems.filter(item => item.featured);
  }

  filterByCategory(category: string) {
    this.selectedCategory = category;
  }

  getProgressPercentage(item: RaffleItem): number {
    return Math.round((item.soldTickets / item.totalTickets) * 100);
  }

  getTimeRemaining(endDate: Date): string {
    const now = new Date();
    const diff = endDate.getTime() - now.getTime();
    
    if (diff <= 0) {
      return 'Ended';
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  enterRaffle(item: RaffleItem) {
    // This would integrate with payment processing
    console.log(`Entering raffle for: ${item.title}`);
    alert(`Redirecting to payment for ${item.title} - $${item.price} per ticket`);
  }
}
