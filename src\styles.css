@import "tailwindcss";

/* Custom Chinese New Year themed styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom gradient backgrounds */
.bg-chinese-red {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.bg-chinese-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

/* Festive glow effects */
.glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.glow-red {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

/* Sparkle animation */
@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

.sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

