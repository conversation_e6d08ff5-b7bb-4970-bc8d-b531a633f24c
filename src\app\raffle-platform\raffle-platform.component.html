<!-- Professional Raffle Platform -->
<div class="min-h-screen bg-gray-50">
  
  <!-- Header -->
  <header class="bg-white shadow-lg border-b-4 border-red-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-xl flex items-center justify-center">
            <span class="text-2xl text-white font-bold">🎯</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">RafflePro</h1>
            <p class="text-sm text-red-600 font-medium">Premium Raffles</p>
          </div>
        </div>
        
        <!-- Navigation -->
        <nav class="hidden md:flex space-x-8">
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Home</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Live Raffles</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Winners</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">How it Works</a>
        </nav>
        
        <!-- User Actions -->
        <div class="flex items-center space-x-4">
          <button class="text-gray-700 hover:text-red-600 font-medium transition-colors">Sign In</button>
          <button class="bg-red-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-5xl font-bold mb-6">Win Premium Products</h2>
      <p class="text-xl mb-8 text-red-100 max-w-3xl mx-auto">
        Enter raffles for the latest sneakers, electronics, and exclusive items. 
        Fair chances, transparent draws, instant results.
      </p>
      <div class="flex justify-center space-x-6 text-sm">
        <div class="flex items-center space-x-2">
          <span class="w-2 h-2 bg-green-400 rounded-full"></span>
          <span>Live Draws</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="w-2 h-2 bg-green-400 rounded-full"></span>
          <span>Secure Payments</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="w-2 h-2 bg-green-400 rounded-full"></span>
          <span>Instant Delivery</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Featured Raffles -->
  <section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">🔥 Featured Raffles</h3>
        <p class="text-gray-600 text-lg">Don't miss out on these premium items</p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div *ngFor="let item of featuredRaffles" 
             class="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
          
          <!-- Featured Badge -->
          <div class="relative">
            <div class="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-bold z-10">
              FEATURED
            </div>
            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <span class="text-6xl">{{ item.image }}</span>
            </div>
          </div>
          
          <!-- Card Content -->
          <div class="p-6">
            <h4 class="text-xl font-bold text-gray-900 mb-2">{{ item.title }}</h4>
            <p class="text-gray-600 mb-4">{{ item.description }}</p>
            
            <!-- Progress Bar -->
            <div class="mb-4">
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>{{ item.soldTickets }}/{{ item.totalTickets }} tickets sold</span>
                <span>{{ getProgressPercentage(item) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-600 h-2 rounded-full transition-all duration-300" 
                     [style.width.%]="getProgressPercentage(item)"></div>
              </div>
            </div>
            
            <!-- Price and Time -->
            <div class="flex justify-between items-center mb-4">
              <div>
                <span class="text-2xl font-bold text-red-600">${{ item.price }}</span>
                <span class="text-gray-500 text-sm">/ticket</span>
              </div>
              <div class="text-right">
                <div class="text-sm text-gray-500">Ends in</div>
                <div class="font-bold text-gray-900">{{ getTimeRemaining(item.endDate) }}</div>
              </div>
            </div>
            
            <!-- Enter Button -->
            <button (click)="enterRaffle(item)"
                    class="w-full bg-red-600 text-white py-3 rounded-lg font-bold hover:bg-red-700 transition-colors transform hover:scale-105 duration-200">
              Enter Raffle
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Category Filter -->
  <section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-gray-900 mb-4">All Live Raffles</h3>
        
        <!-- Category Buttons -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
          <button (click)="filterByCategory('all')"
                  [class]="selectedCategory === 'all' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
                  class="px-6 py-2 rounded-full font-medium transition-colors">
            All Categories
          </button>
          <button (click)="filterByCategory('sneakers')"
                  [class]="selectedCategory === 'sneakers' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
                  class="px-6 py-2 rounded-full font-medium transition-colors">
            👟 Sneakers
          </button>
          <button (click)="filterByCategory('electronics')"
                  [class]="selectedCategory === 'electronics' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
                  class="px-6 py-2 rounded-full font-medium transition-colors">
            📱 Electronics
          </button>
          <button (click)="filterByCategory('gaming')"
                  [class]="selectedCategory === 'gaming' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
                  class="px-6 py-2 rounded-full font-medium transition-colors">
            🎮 Gaming
          </button>
          <button (click)="filterByCategory('fashion')"
                  [class]="selectedCategory === 'fashion' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
                  class="px-6 py-2 rounded-full font-medium transition-colors">
            👕 Fashion
          </button>
        </div>
      </div>
      
      <!-- All Raffles Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div *ngFor="let item of filteredRaffles" 
             class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100">
          
          <div class="h-40 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <span class="text-4xl">{{ item.image }}</span>
          </div>
          
          <div class="p-4">
            <h4 class="font-bold text-gray-900 mb-1 text-sm">{{ item.title }}</h4>
            <p class="text-gray-600 text-xs mb-3">{{ item.description }}</p>
            
            <!-- Mini Progress -->
            <div class="mb-3">
              <div class="w-full bg-gray-200 rounded-full h-1.5">
                <div class="bg-red-600 h-1.5 rounded-full" 
                     [style.width.%]="getProgressPercentage(item)"></div>
              </div>
              <div class="text-xs text-gray-500 mt-1">{{ getProgressPercentage(item) }}% sold</div>
            </div>
            
            <div class="flex justify-between items-center mb-3">
              <span class="text-lg font-bold text-red-600">${{ item.price }}</span>
              <span class="text-xs text-gray-500">{{ getTimeRemaining(item.endDate) }}</span>
            </div>
            
            <button (click)="enterRaffle(item)"
                    class="w-full bg-red-600 text-white py-2 rounded-lg text-sm font-bold hover:bg-red-700 transition-colors">
              Enter
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <span class="text-2xl">🎯</span>
            <span class="text-xl font-bold">RafflePro</span>
          </div>
          <p class="text-gray-400">The most trusted raffle platform for premium products.</p>
        </div>
        <div>
          <h5 class="font-bold mb-4">Platform</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">How it Works</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Fair Play</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Support</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Terms</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Connect</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Twitter</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Instagram</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Discord</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2024 RafflePro. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
