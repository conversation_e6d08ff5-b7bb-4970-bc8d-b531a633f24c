<!-- Simple Raffle Platform -->
<div class="min-h-screen bg-gray-50">

  <!-- Header -->
  <header class="bg-white shadow-lg border-b-4 border-red-600">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-xl flex items-center justify-center">
            <span class="text-2xl text-white font-bold">🎯</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">WinBig</h1>
            <p class="text-sm text-red-600 font-medium">Top 3 Prizes</p>
          </div>
        </div>

        <!-- User Actions -->
        <div class="flex items-center space-x-4">
          <button class="text-gray-700 hover:text-red-600 font-medium transition-colors">Sign In</button>
          <button class="bg-red-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-5xl font-bold mb-6">Win Life-Changing Prizes</h2>
      <p class="text-xl mb-8 text-red-100 max-w-3xl mx-auto">
        Three incredible prizes. One lucky winner each. Enter now for your chance to win big.
      </p>
    </div>
  </section>

  <!-- Top 3 Prizes -->
  <section class="py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h3 class="text-4xl font-bold text-gray-900 mb-4">🏆 Top 3 Prizes</h3>
        <p class="text-gray-600 text-lg">Three life-changing prizes. Enter now for your chance to win.</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div *ngFor="let item of topThreeRaffles; let i = index"
             class="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
             [class.lg:scale-110]="item.rank === 1"
             [class.lg:-mt-8]="item.rank === 1">

          <!-- Rank Badge -->
          <div class="relative">
            <div class="absolute top-4 left-4 z-10"
                 [class]="item.rank === 1 ? 'bg-yellow-500' : item.rank === 2 ? 'bg-gray-400' : 'bg-orange-500'"
                 class="text-white px-3 py-1 rounded-full text-sm font-bold">
              #{{ item.rank }} PRIZE
            </div>

            <!-- Prize Image -->
            <div class="h-64 bg-gray-100 overflow-hidden">
              <img [src]="item.imageUrl"
                   [alt]="item.title"
                   class="w-full h-full object-cover">
            </div>
          </div>

          <!-- Card Content -->
          <div class="p-6">
            <!-- Prize Value -->
            <div class="text-center mb-4">
              <div class="text-3xl font-black text-red-600 mb-1">{{ item.value }}</div>
              <div class="text-sm text-gray-500 uppercase tracking-wide">Prize Value</div>
            </div>

            <h4 class="text-xl font-bold text-gray-900 mb-2 text-center">{{ item.title }}</h4>
            <p class="text-gray-600 mb-6 text-center text-sm">{{ item.description }}</p>

            <!-- Progress Bar -->
            <div class="mb-6">
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>{{ item.soldTickets }}/{{ item.totalTickets }} tickets sold</span>
                <span>{{ getProgressPercentage(item) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-red-600 h-3 rounded-full transition-all duration-300"
                     [style.width.%]="getProgressPercentage(item)"></div>
              </div>
            </div>

            <!-- Price and Time -->
            <div class="flex justify-between items-center mb-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">${{ item.price }}</div>
                <div class="text-sm text-gray-500">per ticket</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-bold text-gray-900">{{ getTimeRemaining(item.endDate) }}</div>
                <div class="text-sm text-gray-500">remaining</div>
              </div>
            </div>

            <!-- Enter Button -->
            <button (click)="enterRaffle(item)"
                    class="w-full bg-red-600 text-white py-4 rounded-lg font-bold hover:bg-red-700 transition-colors transform hover:scale-105 duration-200 text-lg">
              Enter Raffle Now
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works -->
  <section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h3 class="text-3xl font-bold text-gray-900 mb-12">How It Works</h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">🎫</span>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">1. Buy Tickets</h4>
          <p class="text-gray-600">Choose your prize and purchase tickets. More tickets = better chances!</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">⏰</span>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">2. Wait for Draw</h4>
          <p class="text-gray-600">Sit back and wait for the draw date. We'll notify you when it's time!</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">🏆</span>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">3. Win Big</h4>
          <p class="text-gray-600">Winners are selected randomly and fairly. Could be you!</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <span class="text-2xl">🎯</span>
            <span class="text-xl font-bold">RafflePro</span>
          </div>
          <p class="text-gray-400">The most trusted raffle platform for premium products.</p>
        </div>
        <div>
          <h5 class="font-bold mb-4">Platform</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">How it Works</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Fair Play</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Support</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Terms</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Connect</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Twitter</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Instagram</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Discord</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2024 RafflePro. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
