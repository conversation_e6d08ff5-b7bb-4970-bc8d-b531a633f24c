/* Winner Card Animations */
.winner-card {
  animation: slideInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 1.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced hover effects */
.winner-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 30px rgba(251, 191, 36, 0.3);
}

.winner-card:hover .bg-gradient-to-br {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

/* Pulse effect for rank badges */
.winner-card:nth-child(1) .bg-gradient-to-r {
  animation: goldPulse 2s ease-in-out infinite;
}

@keyframes goldPulse {
  0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
  50% { box-shadow: 0 0 30px rgba(251, 191, 36, 0.8); }
}

/* Shimmer effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Celebration text animation */
.celebration-text {
  animation: celebrationBounce 2s ease-in-out infinite;
}

@keyframes celebrationBounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

/* Festive glow effects */
.glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.glow-red {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

/* Sparkle animation */
.sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

/* Floating animation */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Custom border width */
.border-3 {
  border-width: 3px;
}

/* Custom shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}
