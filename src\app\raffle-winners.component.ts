import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Winner {
  rank: number;
  name: string;
  prize: string;
  emoji: string;
  rankEmoji: string;
  initials: string;
}

@Component({
  selector: 'app-raffle-winners',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="bg-blue-500 text-white p-8 text-center">
      <h2 class="text-4xl font-bold">RAFFLE COMPONENT LOADED!</h2>
      <p class="mt-4">If you see this blue box, the component is working!</p>
    </div>
    <div class="min-h-screen bg-gradient-to-br from-red-50 via-red-100 to-yellow-50 py-8 px-4 relative overflow-hidden">
      <!-- Background Decorative Elements -->
      <div class="absolute top-10 left-10 text-6xl opacity-10 sparkle">🏮</div>
      <div class="absolute top-32 right-16 text-4xl opacity-10 sparkle" style="animation-delay: 1s">✨</div>
      <div class="absolute bottom-20 left-20 text-5xl opacity-10 sparkle" style="animation-delay: 2s">🎊</div>
      <div class="absolute bottom-40 right-10 text-3xl opacity-10 sparkle" style="animation-delay: 1.5s">🎉</div>

      <!-- Festive Header -->
      <div class="text-center mb-12 animate-fade-in relative z-10">
        <div class="mb-6">
          <span class="text-6xl md:text-8xl float">🏮</span>
        </div>
        <h1 class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-red-700 via-red-600 to-red-700 bg-clip-text text-transparent mb-4 drop-shadow-lg">
          Lucky Draw Winners
        </h1>
        <div class="flex justify-center items-center space-x-4 mb-4">
          <div class="w-16 h-1 bg-gradient-to-r from-transparent to-yellow-500 rounded-full"></div>
          <span class="text-3xl">🎊</span>
          <div class="w-16 h-1 bg-gradient-to-r from-yellow-500 to-transparent rounded-full"></div>
        </div>
        <p class="text-red-600 font-medium text-lg">Celebrating our lucky winners!</p>
      </div>

      <!-- Winners Container -->
      <div class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div 
            *ngFor="let winner of winners; let i = index" 
            class="winner-card transform transition-all duration-500 hover:scale-105"
            [style.animation-delay]="(i * 200) + 'ms'"
          >
            <!-- Winner Card -->
            <div class="bg-white rounded-xl shadow-2xl border-3 border-yellow-400 overflow-hidden glow-gold transform transition-all duration-300 hover:shadow-3xl shimmer">
              <!-- Rank Badge -->
              <div class="bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white text-center py-4 relative">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-10"></div>
                <div class="text-2xl font-bold relative z-10">{{ winner.rankEmoji }} {{ winner.rank }}{{ getOrdinalSuffix(winner.rank) }} Place</div>
              </div>

              <!-- Winner Content -->
              <div class="p-6 text-center bg-gradient-to-b from-white to-red-50">
                <!-- Avatar/Initials -->
                <div class="w-24 h-24 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-xl border-4 border-white glow-gold">
                  <span class="text-2xl font-bold text-red-800">{{ winner.initials }}</span>
                </div>

                <!-- Winner Name -->
                <h3 class="text-2xl font-bold bg-gradient-to-r from-red-700 to-red-800 bg-clip-text text-transparent mb-4 drop-shadow-sm">{{ winner.name }}</h3>

                <!-- Prize Image Placeholder -->
                <div class="w-28 h-28 bg-gradient-to-br from-yellow-100 via-yellow-200 to-yellow-300 rounded-xl flex items-center justify-center mx-auto mb-4 border-3 border-yellow-400 shadow-lg glow-gold hover:scale-110 transition-transform duration-300">
                  <span class="text-4xl drop-shadow-sm">{{ winner.emoji }}</span>
                </div>

                <!-- Prize Name -->
                <div class="bg-gradient-to-r from-yellow-50 via-yellow-100 to-yellow-50 rounded-xl p-4 border-2 border-yellow-400 shadow-inner">
                  <h4 class="text-lg font-bold text-red-700 drop-shadow-sm">{{ winner.prize }}</h4>
                  <div class="text-sm text-red-600 mt-1 font-medium">🎉 Congratulations! 🎉</div>
                </div>
              </div>

              <!-- Decorative Bottom Border with Pattern -->
              <div class="h-3 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 relative">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Confetti Animation Area -->
        <div class="text-center mb-8 relative">
          <div class="inline-flex space-x-4 text-3xl">
            <span class="sparkle text-yellow-500">✨</span>
            <span class="animate-bounce text-red-500" style="animation-delay: 0.2s">🎉</span>
            <span class="sparkle text-yellow-500" style="animation-delay: 0.4s">✨</span>
            <span class="animate-bounce text-red-500" style="animation-delay: 0.6s">🎊</span>
            <span class="sparkle text-yellow-500" style="animation-delay: 0.8s">✨</span>
            <span class="animate-bounce text-red-500" style="animation-delay: 1s">🎉</span>
            <span class="sparkle text-yellow-500" style="animation-delay: 1.2s">✨</span>
          </div>
          <!-- Floating celebration text -->
          <div class="mt-4">
            <span class="text-2xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent celebration-text">
              🏆 CELEBRATION TIME! 🏆
            </span>
          </div>
        </div>

        <!-- Footer Message -->
        <div class="text-center bg-gradient-to-r from-white via-red-50 to-white rounded-xl shadow-2xl p-8 border-3 border-yellow-400 glow-gold relative overflow-hidden">
          <!-- Background decoration -->
          <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-yellow-100/20 to-red-100/20 pointer-events-none"></div>

          <div class="relative z-10">
            <div class="text-6xl mb-4 float">🎊</div>
            <p class="text-xl font-bold bg-gradient-to-r from-red-700 to-red-800 bg-clip-text text-transparent mb-3">
              Congratulations to all our winners!
            </p>
            <p class="text-red-600 font-medium text-lg mb-4">
              More winners will be announced soon!
            </p>
            <div class="flex justify-center space-x-2 text-2xl">
              <span class="sparkle">🏮</span>
              <span class="sparkle" style="animation-delay: 0.5s">🎁</span>
              <span class="sparkle" style="animation-delay: 1s">🏮</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .winner-card {
      animation: slideInUp 0.8s ease-out forwards;
      opacity: 0;
      transform: translateY(30px);
    }

    @keyframes slideInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-fade-in {
      animation: fadeIn 1.2s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Enhanced hover effects */
    .winner-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 30px rgba(251, 191, 36, 0.3);
    }

    .winner-card:hover .bg-gradient-to-br {
      transform: scale(1.1);
      transition: transform 0.3s ease;
    }

    /* Pulse effect for rank badges */
    .winner-card:nth-child(1) .bg-gradient-to-r {
      animation: goldPulse 2s ease-in-out infinite;
    }

    @keyframes goldPulse {
      0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
      50% { box-shadow: 0 0 30px rgba(251, 191, 36, 0.8); }
    }

    /* Shimmer effect */
    .shimmer {
      position: relative;
      overflow: hidden;
    }

    .shimmer::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    /* Celebration text animation */
    .celebration-text {
      animation: celebrationBounce 2s ease-in-out infinite;
    }

    @keyframes celebrationBounce {
      0%, 100% { transform: translateY(0) scale(1); }
      50% { transform: translateY(-5px) scale(1.05); }
    }
  `]
})
export class RaffleWinnersComponent {
  winners: Winner[] = [
    {
      rank: 1,
      name: 'Alice Chen',
      prize: 'iPhone 15 Pro',
      emoji: '📱',
      rankEmoji: '🥇',
      initials: 'AC'
    },
    {
      rank: 2,
      name: 'Bob Wang',
      prize: 'Apple Watch',
      emoji: '⌚',
      rankEmoji: '🥈',
      initials: 'BW'
    },
    {
      rank: 3,
      name: 'Carol Liu',
      prize: 'Amazon Gift Card',
      emoji: '🎁',
      rankEmoji: '🥉',
      initials: 'CL'
    }
  ];

  getOrdinalSuffix(rank: number): string {
    const suffixes = ['st', 'nd', 'rd'];
    return suffixes[rank - 1] || 'th';
  }
}
